import os
import pandas as pd
from pymongo import MongoClient
from datetime import datetime as dt
from datetime import timed<PERSON>ta
from dateutil import parser

uri = "mongodb+srv://GaudKing_Chapper:<EMAIL>/captains_log?retryWrites=true&w=majority"
conn = MongoClient(uri)

# check status of the following numbers
pull = conn.get_database("captains_log")

available = []

col = pull.get_collection(name="snr_status")
test_list = [944,948, 947, 951, 788, 787, 766, 755, 744, 355, 358, 359, 366, 488, 476, 466, 499, 476]
for i in test_list:
    cursor = col.find({'SNR':str(i)})
    if cursor[0]['STATUS']=="AVAILABLE":
        available.append(cursor[0])

available

# A simple representation of the team members in each role.
# In a real system, this could be queried from a database.
team = {
    "Technical Analyst": "<PERSON>",
    "Senior Technical Analyst": "<PERSON>",
    "Principal Technical Specialist": "<PERSON>",
    "Licensing Analyst": "<PERSON>",
    "Senior Economic & Policy Analyst": "<PERSON>",
    "Principal Economic & Policy Specialist": "<PERSON>",
    "Unit Head": "Grace"
}

def route_task(task_type, complexity, details=None):
    """
    Determines the correct person or sequence of people to handle a new task.

    Args:
        task_type (str): The category of the task. 
                         Expected values: 'numbering', 'licensing', 'invoicing', 
                                          'consultation', 'strategy'.
        complexity (str): The difficulty or scope of the task.
                          Expected values: 'standard', 'complex', 'strategic'.
        details (dict, optional): A dictionary for extra information, 
                                  like {'is_new_service': True}. Defaults to None.

    Returns:
        str: A string describing the assignment and the initial action.
    """
    if details is None:
        details = {}

    # --- ROUTING LOGIC ---

    # 1. Standard, day-to-day tasks
    if complexity == 'standard':
        if task_type == 'numbering':
            # As in our scenario, standard numbering requests need both technical and commercial checks.
            assignee_tech = team['Technical Analyst']
            assignee_license = team['Licensing Analyst']
            return (f"ASSIGNED TO: {assignee_tech} (Technical) and {assignee_license} (Licensing).\n"
                    f"ACTION: Perform parallel review. {assignee_tech} to check NNP, "
                    f"{assignee_license} to check licensee status and prep invoice.")

        if task_type == 'licensing':
            # e.g., a simple license renewal with no changes.
            assignee = team['Licensing Analyst']
            return (f"ASSIGNED TO: {assignee}.\n"
                    f"ACTION: Process license renewal and generate standard invoice.")

        if task_type == 'invoicing':
            # e.g., a query about a recent bill.
            assignee = team['Licensing Analyst']
            return (f"ASSIGNED TO: {assignee}.\n"
                    f"ACTION: Address invoice query and update billing records.")

    # 2. Complex, non-standard tasks requiring senior expertise
    elif complexity == 'complex':
        if task_type == 'numbering':
            # e.g., a request for a large, unusual block of numbers or for a new type of service.
            assignee = team['Senior Technical Analyst']
            return (f"ASSIGNED TO: {assignee}.\n"
                    f"ACTION: Analyze technical feasibility and impact on NNP. "
                    f"Consult with Senior Economic & Policy Analyst if needed.")

        if task_type == 'licensing':
            # e.g., drafting a new license based on an existing framework but with unique conditions.
            assignee = team['Senior Economic & Policy Analyst']
            return (f"ASSIGNED TO: {assignee}.\n"
                    f"ACTION: Draft bespoke license conditions. "
                    f"Consult with Senior Technical Analyst on technical clauses.")
        
        if task_type == 'consultation':
            # e.g., running a public consultation on a draft license.
            assignee = team['Senior Economic & Policy Analyst']
            return (f"ASSIGNED TO: {assignee}.\n"
                    f"ACTION: Manage public consultation process, collate, and analyze feedback.")


    # 3. Strategic, high-level, and future-proofing tasks
    elif complexity == 'strategic':
        if task_type == 'strategy' and details.get('domain') == 'technical':
            # e.g., "Review the entire National Numbering Plan for IoT readiness."
            assignee = team['Principal Technical Specialist']
            return (f"ASSIGNED TO: {assignee} (Lead).\n"
                    f"ACTION: Form a project team to conduct a strategic review of the NNP. "
                    f"Report findings to {team['Unit Head']}.")

        if task_type == 'strategy' and details.get('domain') == 'economic':
            # e.g., "Develop a new licensing framework for satellite broadband services."
            assignee = team['Principal Economic & Policy Specialist']
            return (f"ASSIGNED TO: {assignee} (Lead).\n"
                    f"ACTION: Form a project team to develop a new licensing framework. "
                    f"Report findings to {team['Unit Head']}.")

    # Default case for unhandled combinations
    return f"UNROUTED: Task type '{task_type}' with complexity '{complexity}' needs manual review by {team['Unit Head']}."


# --- EXAMPLE USAGE ---
print("--- Daily Task Routing Examples ---")

# Scenario 1: The example from our narrative
print("TASK 1: Standard Numbering Application")
task1_details = route_task('numbering', 'standard')
print(task1_details)
print("-" * 30)

# Scenario 2: A simple license renewal
print("TASK 2: Standard License Renewal")
task2_details = route_task('licensing', 'standard')
print(task2_details)
print("-" * 30)

# Scenario 3: A complex request for numbers for a new drone service
print("TASK 3: Complex Numbering for New Service")
task3_details = route_task('numbering', 'complex', details={'is_new_service': True})
print(task3_details)
print("-" * 30)

# Scenario 4: A major strategic initiative
print("TASK 4: Develop a new 5G Licensing Framework")
task4_details = route_task('strategy', 'strategic', details={'domain': 'economic'})
print(task4_details)
print("-" * 30)
