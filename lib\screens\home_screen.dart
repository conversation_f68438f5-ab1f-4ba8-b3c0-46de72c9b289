import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../widgets/home_search_bar.dart';
import '../widgets/post_card.dart';
import 'package:wicker/services/post_service.dart';

class HomeScreen extends StatefulWidget {
  final VoidCallback onSearchTap;
  const HomeScreen({super.key, required this.onSearchTap});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final PostService _postService = PostService();
  late Future<List<Map<String, dynamic>>> _postsFuture;

  final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
      ? "http://10.0.2.2:5000"
      : "http://127.0.0.1:5000";

  @override
  void initState() {
    super.initState();
    // THE FIX: Call the correctly named getPosts method
    _postsFuture = _postService.getPosts();
  }

  // THE FIX: This new helper processes "post" data, not "place" data
  List<Map<String, dynamic>> _processFetchedPosts(
    List<Map<String, dynamic>> posts,
  ) {
    for (var post in posts) {
      // Map post fields to what PostCard expects
      post['title'] = post['text_content'] ?? 'No title';

      if (post['media_paths'] != null &&
          (post['media_paths'] as List).isNotEmpty) {
        String imagePath = post['media_paths'][0];
        String correctedPath = imagePath.replaceAll('\\', '/');
        post['imageUrl'] = '$_baseUrl/$correctedPath';
      } else {
        post['imageUrl'] =
            'https://via.placeholder.com/400x300.png?text=No+Media';
      }

      // Use the author details from the backend
      final authorDetails = post['author_details'] as Map<String, dynamic>?;
      post['posterName'] = authorDetails?['username'] ?? 'A User';
      // TODO: Add real avatar URL to user model later
      post['avatarUrl'] =
          'https://picsum.photos/seed/${authorDetails?['_id']['\$oid']}/100';

      post['views'] = (post['comments'] as List).length.toString();
      post['postedTime'] = 'Just now'; // TODO: Implement time-ago logic
    }
    return posts;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: FutureBuilder<List<Map<String, dynamic>>>(
        future: _postsFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No posts found. Create one!'));
          }

          final allPosts = _processFetchedPosts(snapshot.data!);

          return Column(
            children: [
              Container(
                padding: const EdgeInsets.only(top: 40.0, bottom: 8.0),
                child: HomeSearchBar(onTap: widget.onSearchTap),
              ),
              Expanded(
                child: ListView.builder(
                  padding: EdgeInsets.zero,
                  itemCount: allPosts.length,
                  itemBuilder: (context, index) {
                    final post = allPosts[index];
                    return PostCard(
                      postData: post.map((k, v) => MapEntry(k, v.toString())),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
