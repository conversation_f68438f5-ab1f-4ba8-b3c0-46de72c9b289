import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:wicker/screens/comments_modal.dart';
import 'package:wicker/widgets/add_to_playlist_modal.dart';
import 'package:wicker/widgets/aspect_ratio_container.dart';
import 'package:flutter/foundation.dart';

class PostCard extends StatelessWidget {
  final Map<String, String> postData;

  const PostCard({super.key, required this.postData});

  @override
  _PostCardState createState() => _PostCardState();
}


class _PostCardState extends State<PostCard> {
  // State to keep track of the current page in the carousel
  int _currentPage = 0;

  @override
  Widget build(BuildContext context) {
    // Parse media list from the post data string
    final mediaJsonString = widget.postData['media'] ?? '[]';
    final List<dynamic> mediaList = jsonDecode(mediaJsonString);
    final hasMedia = mediaList.isNotEmpty;
    
    // Base URL for constructing image paths
    final String baseUrl = defaultTargetPlatform == TargetPlatform.android
        ? "http://10.0.2.2:5000"
        : "http://127.0.0.1:5000";

    // Using ?? to provide default values and prevent null errors
    final imageUrl =
        postData['imageUrl'] ??
        'https://via.placeholder.com/400x210.png?text=No+Image';
    final avatarUrl =
        postData['avatarUrl'] ?? 'https://via.placeholder.com/150.png?text=N/A';
    final title = postData['title'] ?? 'Untitled Post';
    final posterName = postData['posterName'] ?? 'Unknown';
    final views = postData['views'] ?? '0';
    final postedTime = postData['postedTime'] ?? 'long ago';

    // 2. Parse the aspect ratio from the post data
    final mediaList = postData['media'] as List<dynamic>? ?? [];
    final hasMedia = mediaList.isNotEmpty;
    final aspectRatio = hasMedia
        ? (mediaList[0]['aspect_ratio'] as double? ?? 1.0)
        : 1.0;

    return Card(
      elevation: 0,
      margin: const EdgeInsets.all(0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (hasMedia)
            Column(
              children: [
                SizedBox(
                  height: 300, // Give the carousel a fixed height
                  child: PageView.builder(
                    itemCount: mediaList.length,
                    onPageChanged: (index) {
                      setState(() {
                        _currentPage = index;
                      });
                    },
                    itemBuilder: (context, index) {
                      final mediaItem = mediaList[index] as Map<String, dynamic>;
                      final imagePath = mediaItem['path']?.replaceAll('\\', '/') ?? '';
                      final imageUrl = '$baseUrl/$imagePath';
                      final aspectRatio = mediaItem['aspect_ratio'] as double? ?? 1.0;
                      
                      return AspectRatioContainer(
                        imageUrl: imageUrl,
                        aspectRatio: aspectRatio,
                      );
                    },
                  ),
                ),
                // Show indicators only if there's more than one item
                if (mediaList.length > 1)
                  _buildCarouselIndicator(mediaList.length),
              ],
            ),
          Padding(
            padding: const EdgeInsets.fromLTRB(12, 12, 12, 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: NetworkImage(avatarUrl),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        "$posterName • $views views • $postedTime",
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // NEW: Engagement buttons row
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(EvaIcons.arrowUpwardOutline),
                      onPressed: () {
                        print('Like button tapped');
                      },
                    ),
                    IconButton(
                      icon: const Icon(EvaIcons.arrowDownwardOutline),
                      onPressed: () {
                        print('Dislike button tapped');
                      },
                    ),
                    IconButton(
                      icon: const Icon(EvaIcons.messageSquareOutline),
                      onPressed: () {
                        // THE CHANGE: Show the comments modal as a bottom sheet
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled:
                              true, // Allows the sheet to be full screen
                          builder: (context) {
                            // We use a fraction of the screen height
                            return FractionallySizedBox(
                              heightFactor: 0.9,
                              child: const CommentsModal(),
                            );
                          },
                        );
                      },
                    ),
                    IconButton(
                      icon: const Icon(EvaIcons.bookmarkOutline),
                      onPressed: () {
                        showModalBottomSheet(
                          context: context,
                          builder: (context) => const AddToPlaylistModal(),
                        );
                      },
                    ),
                  ],
                ),
                IconButton(
                  icon: const Icon(EvaIcons.shareOutline),
                  onPressed: () {
                    print('Share button tapped');
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
