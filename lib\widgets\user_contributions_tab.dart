import 'package:flutter/material.dart';
import 'package:wicker/services/post_service.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wicker/widgets/explore_post_card.dart';
import 'package:flutter/foundation.dart';

class UserContributionsTab extends StatefulWidget {
  const UserContributionsTab({super.key});

  @override
  State<UserContributionsTab> createState() => _UserContributionsTabState();
}

class _UserContributionsTabState extends State<UserContributionsTab> {
  final PostService _postService = PostService();
  late Future<List<Map<String, dynamic>>> _myPostsFuture;

  @override
  void initState() {
    super.initState();
    _myPostsFuture = _postService
        .getMyPosts(); // Assumes getMyPosts exists in PostService
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _myPostsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(child: Text('You haven\'t made any posts yet.'));
        }

        final posts = snapshot.data!;
        final String baseUrl = defaultTargetPlatform == TargetPlatform.android
            ? "http://10.0.2.2:5000"
            : "http://127.0.0.1:5000";

        return MasonryGridView.count(
          crossAxisCount: 3,
          mainAxisSpacing: 4,
          crossAxisSpacing: 4,
          padding: const EdgeInsets.all(4.0),
          itemCount: posts.length,
          itemBuilder: (context, index) {
            final post = posts[index];
            if ((post['media_paths'] as List).isNotEmpty) {
              String imagePath = post['media_paths'][0];
              post['imageUrl'] = '$baseUrl/${imagePath.replaceAll('\\', '/')}';
            }
            post['title'] = post['text_content'];
            return ExplorePostCard(
              postData: post.map((k, v) => MapEntry(k, v.toString())),
            );
          },
        );
      },
    );
  }
}
